/* Font Loading Fix for Bengali Fonts */

/* Preload critical fonts */
@font-face {
    font-family: 'Kalpurush-Local';
    src: local('Kalpurush'),
         local('Kalpurush Regular'),
         url('https://fonts.bunny.net/kalpurush/files/kalpurush-latin-400-normal.woff2') format('woff2'),
         url('https://fonts.bunny.net/kalpurush/files/kalpurush-latin-400-normal.woff') format('woff');
    font-weight: 400;
    font-style: normal;
    font-display: swap; /* This ensures text is visible during font load */
}

@font-face {
    font-family: 'Kalpurush-Local';
    src: local('Kalpurush Medium'),
         local('Kalpurush-Medium'),
         url('https://fonts.bunny.net/kalpurush/files/kalpurush-latin-500-normal.woff2') format('woff2'),
         url('https://fonts.bunny.net/kalpurush/files/kalpurush-latin-500-normal.woff') format('woff');
    font-weight: 500;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Kalpurush-Local';
    src: local('Kalpurush SemiBold'),
         local('Kalpurush-SemiBold'),
         url('https://fonts.bunny.net/kalpurush/files/kalpurush-latin-600-normal.woff2') format('woff2'),
         url('https://fonts.bunny.net/kalpurush/files/kalpurush-latin-600-normal.woff') format('woff');
    font-weight: 600;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Kalpurush-Local';
    src: local('Kalpurush Bold'),
         local('Kalpurush-Bold'),
         url('https://fonts.bunny.net/kalpurush/files/kalpurush-latin-700-normal.woff2') format('woff2'),
         url('https://fonts.bunny.net/kalpurush/files/kalpurush-latin-700-normal.woff') format('woff');
    font-weight: 700;
    font-style: normal;
    font-display: swap;
}

/* Fallback font definitions */
@font-face {
    font-family: 'NotoSansBengali-Fallback';
    src: local('Noto Sans Bengali'),
         local('NotoSansBengali-Regular');
    font-weight: 400;
    font-style: normal;
    font-display: swap;
}

/* Enhanced font stack with better fallbacks */
:root {
    --primary-bengali-font: 'Kalpurush-Local', 'Kalpurush', 'NotoSansBengali-Fallback', 'Noto Sans Bengali', 'SolaimanLipi', 'Nikosh', 'Vrinda', 'Shonar Bangla', 'Bangla', sans-serif;
    --fallback-bengali-font: 'NotoSansBengali-Fallback', 'Noto Sans Bengali', 'SolaimanLipi', 'Nikosh', 'Vrinda', 'Shonar Bangla', 'Bangla', sans-serif;
    --number-font: 'Kalpurush-Local', 'Kalpurush', 'NotoSansBengali-Fallback', 'Noto Sans Bengali', 'Roboto', 'Arial', sans-serif;
    --english-font: 'Poppins', 'Roboto', 'Segoe UI', 'Arial', sans-serif;
}

/* Font loading states */
.font-loading {
    font-family: var(--fallback-bengali-font) !important;
    visibility: visible !important;
}

.font-loaded {
    font-family: var(--primary-bengali-font) !important;
}

/* Ensure text is always visible */
body {
    font-family: var(--primary-bengali-font);
    font-display: swap;
}

/* Override existing font variables */
body:not(.kalpurush-fallback) {
    --bengali-font: var(--primary-bengali-font);
    --number-font: var(--number-font);
}

body.kalpurush-fallback {
    --bengali-font: var(--fallback-bengali-font);
    --number-font: var(--fallback-bengali-font);
}

/* Specific fixes for critical elements */
.loader-text h2,
.loader-text p,
.bengali-text,
h1, h2, h3, h4, h5, h6 {
    font-family: var(--primary-bengali-font) !important;
    font-display: swap !important;
}

/* Number and amount displays */
[class*="amount"], 
[class*="balance"], 
[class*="total"],
.currency, 
.price, 
.value, 
.count, 
.number {
    font-family: var(--number-font) !important;
    font-display: swap !important;
}

/* Ensure Font Awesome icons work properly */
[class^="fa-"], 
[class*=" fa-"], 
.icon, 
[class*="icon-"] {
    font-family: "Font Awesome 6 Free", "Font Awesome 6 Pro", "Font Awesome 6 Brands" !important;
    font-weight: 900 !important;
    font-style: normal !important;
}

/* Critical text elements that must be readable immediately */
.critical-text {
    font-family: var(--fallback-bengali-font) !important;
    font-display: block !important;
}

/* Animation for smooth font transition */
@keyframes fontFadeIn {
    from {
        opacity: 0.7;
    }
    to {
        opacity: 1;
    }
}

.font-transition {
    animation: fontFadeIn 0.3s ease-in-out;
}

/* Media queries for better font rendering on different devices */
@media screen and (max-width: 768px) {
    body {
        font-size: 14px;
        line-height: 1.5;
    }
}

@media screen and (min-width: 1200px) {
    body {
        font-size: 16px;
        line-height: 1.6;
    }
}

/* High DPI display optimizations */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    body {
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        text-rendering: optimizeLegibility;
    }
}
