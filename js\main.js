const { app, BrowserWindow, Menu, shell, dialog, ipcMain } = require('electron');
const path = require('path');
const fs = require('fs');

// Keep a global reference of the window object
let mainWindow;

function createWindow() {
  // Create the browser window
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    minWidth: 800,
    minHeight: 600,
    icon: path.join(__dirname, '..', 'assets', 'icon.ico'),
    webPreferences: {
      nodeIntegration: false,           // Security: Disable node integration
      contextIsolation: true,           // Security: Enable context isolation
      enableRemoteModule: false,        // Security: Disable remote module
      webSecurity: true,                // Security: Enable web security
      allowRunningInsecureContent: false, // Security: Block insecure content
      experimentalFeatures: false,      // Security: Disable experimental features
      preload: path.join(__dirname, 'preload.js') // Use preload script for safe IPC
    },
    show: false, // Don't show until ready
    titleBarStyle: 'default',
    frame: true
  });

  // Load the index.html file
  mainWindow.loadFile('index.html');

  // Show window when ready to prevent visual flash
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
    
    // Focus on window
    if (process.platform === 'darwin') {
      mainWindow.focus();
    }
  });

  // Open DevTools in development
  if (process.argv.includes('--dev')) {
    mainWindow.webContents.openDevTools();
  }

  // Handle window closed
  mainWindow.on('closed', () => {
    mainWindow = null;
  });

  // Handle external links
  mainWindow.webContents.setWindowOpenHandler(({ url }) => {
    shell.openExternal(url);
    return { action: 'deny' };
  });

  // Prevent navigation to external URLs
  mainWindow.webContents.on('will-navigate', (event, navigationUrl) => {
    const parsedUrl = new URL(navigationUrl);
    
    if (parsedUrl.origin !== 'file://') {
      event.preventDefault();
      shell.openExternal(navigationUrl);
    }
  });
}

// Create application menu
function createMenu() {
  const template = [
    {
      label: 'ফাইল',
      submenu: [
        {
          label: 'নতুন',
          accelerator: 'CmdOrCtrl+N',
          click: () => {
            // Add new functionality here
          }
        },
        {
          label: 'খুলুন',
          accelerator: 'CmdOrCtrl+O',
          click: () => {
            // Add open functionality here
          }
        },
        {
          label: 'সেভ করুন',
          accelerator: 'CmdOrCtrl+S',
          click: () => {
            // Add save functionality here
          }
        },
        { type: 'separator' },
        {
          label: 'প্রিন্ট',
          accelerator: 'CmdOrCtrl+P',
          click: () => {
            mainWindow.webContents.print();
          }
        },
        { type: 'separator' },
        {
          label: 'বন্ধ করুন',
          accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
          click: () => {
            app.quit();
          }
        }
      ]
    },
    {
      label: 'সম্পাদনা',
      submenu: [
        { label: 'আনডু', accelerator: 'CmdOrCtrl+Z', role: 'undo' },
        { label: 'রিডু', accelerator: 'Shift+CmdOrCtrl+Z', role: 'redo' },
        { type: 'separator' },
        { label: 'কাট', accelerator: 'CmdOrCtrl+X', role: 'cut' },
        { label: 'কপি', accelerator: 'CmdOrCtrl+C', role: 'copy' },
        { label: 'পেস্ট', accelerator: 'CmdOrCtrl+V', role: 'paste' },
        { label: 'সব নির্বাচন', accelerator: 'CmdOrCtrl+A', role: 'selectall' }
      ]
    },
    {
      label: 'দেখুন',
      submenu: [
        { label: 'রিলোড', accelerator: 'CmdOrCtrl+R', role: 'reload' },
        { label: 'ফোর্স রিলোড', accelerator: 'CmdOrCtrl+Shift+R', role: 'forceReload' },
        { label: 'ডেভেলপার টুলস', accelerator: 'F12', role: 'toggleDevTools' },
        { type: 'separator' },
        { label: 'জুম ইন', accelerator: 'CmdOrCtrl+Plus', role: 'zoomIn' },
        { label: 'জুম আউট', accelerator: 'CmdOrCtrl+-', role: 'zoomOut' },
        { label: 'রিসেট জুম', accelerator: 'CmdOrCtrl+0', role: 'resetZoom' },
        { type: 'separator' },
        { label: 'ফুলস্ক্রিন', accelerator: 'F11', role: 'togglefullscreen' }
      ]
    },
    {
      label: 'উইন্ডো',
      submenu: [
        { label: 'মিনিমাইজ', accelerator: 'CmdOrCtrl+M', role: 'minimize' },
        { label: 'বন্ধ', accelerator: 'CmdOrCtrl+W', role: 'close' }
      ]
    },
    {
      label: 'সাহায্য',
      submenu: [
        {
          label: 'সম্পর্কে',
          click: () => {
            dialog.showMessageBox(mainWindow, {
              type: 'info',
              title: 'সম্পর্কে',
              message: 'My Finance App',
              detail: 'একটি বাংলা ফিন্যান্স ম্যানেজমেন্ট অ্যাপ\nডেভেলপার: MD Fahim Haque\nভার্সন: 1.0.0'
            });
          }
        },
        {
          label: 'GitHub এ দেখুন',
          click: () => {
            shell.openExternal('https://github.com');
          }
        }
      ]
    }
  ];

  // macOS specific menu adjustments
  if (process.platform === 'darwin') {
    template.unshift({
      label: app.getName(),
      submenu: [
        { label: 'সম্পর্কে ' + app.getName(), role: 'about' },
        { type: 'separator' },
        { label: 'সেবা', role: 'services', submenu: [] },
        { type: 'separator' },
        { label: app.getName() + ' লুকান', accelerator: 'Command+H', role: 'hide' },
        { label: 'অন্যান্য লুকান', accelerator: 'Command+Shift+H', role: 'hideothers' },
        { label: 'সব দেখান', role: 'unhide' },
        { type: 'separator' },
        { label: 'বন্ধ', accelerator: 'Command+Q', click: () => app.quit() }
      ]
    });
  }

  const menu = Menu.buildFromTemplate(template);
  Menu.setApplicationMenu(menu);
}

// App event listeners
app.whenReady().then(() => {
  createWindow();
  createMenu();

  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      createWindow();
    }
  });
});

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

// Security: Prevent new window creation
app.on('web-contents-created', (event, contents) => {
  contents.on('new-window', (event, navigationUrl) => {
    event.preventDefault();
    shell.openExternal(navigationUrl);
  });
});

// IPC handlers for communication with renderer process
ipcMain.handle('app-version', () => {
  return app.getVersion();
});

// Window control handlers
ipcMain.handle('minimize-window', () => {
  if (mainWindow) {
    mainWindow.minimize();
  }
});

ipcMain.handle('maximize-window', () => {
  if (mainWindow) {
    if (mainWindow.isMaximized()) {
      mainWindow.unmaximize();
    } else {
      mainWindow.maximize();
    }
  }
});

ipcMain.handle('close-window', () => {
  if (mainWindow) {
    mainWindow.close();
  }
});

// Print handler
ipcMain.handle('print-page', () => {
  if (mainWindow) {
    mainWindow.webContents.print();
  }
});

// Notification handler
ipcMain.handle('show-notification', (event, { title, body, ...options }) => {
  const { Notification } = require('electron');

  if (Notification.isSupported()) {
    const notification = new Notification({
      title,
      body,
      ...options
    });

    notification.show();
    return true;
  }
  return false;
});

ipcMain.handle('show-save-dialog', async () => {
  const result = await dialog.showSaveDialog(mainWindow, {
    filters: [
      { name: 'JSON Files', extensions: ['json'] },
      { name: 'All Files', extensions: ['*'] }
    ]
  });
  return result;
});

ipcMain.handle('show-open-dialog', async () => {
  const result = await dialog.showOpenDialog(mainWindow, {
    filters: [
      { name: 'JSON Files', extensions: ['json'] },
      { name: 'All Files', extensions: ['*'] }
    ],
    properties: ['openFile']
  });
  return result;
});

// Handle file operations
ipcMain.handle('write-file', async (event, filePath, data) => {
  try {
    fs.writeFileSync(filePath, data, 'utf8');
    return { success: true };
  } catch (error) {
    return { success: false, error: error.message };
  }
});

ipcMain.handle('read-file', async (event, filePath) => {
  try {
    const data = fs.readFileSync(filePath, 'utf8');
    return { success: true, data };
  } catch (error) {
    return { success: false, error: error.message };
  }
});
