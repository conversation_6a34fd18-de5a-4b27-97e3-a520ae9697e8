/**
 * Enhanced Font Loader for Bengali Fonts
 * Handles font loading with proper fallbacks and error handling
 */

class FontLoader {
    constructor() {
        this.fonts = {
            primary: ['Ka<PERSON>purush', 'Kalpurush-Local'],
            fallback: ['Noto Sans Bengali', 'NotoSansBengali-Fallback'],
            system: ['<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>']
        };
        
        this.loadTimeout = 5000; // 5 seconds timeout
        this.retryCount = 3;
        this.currentRetry = 0;
        
        this.init();
    }
    
    init() {
        // Add initial loading state
        document.body.classList.add('font-loading');
        
        // Start font loading process
        this.loadFonts();
        
        // Set up fallback timer
        setTimeout(() => {
            if (document.body.classList.contains('font-loading')) {
                console.warn('Font loading timeout, using fallbacks');
                this.useFallback();
            }
        }, this.loadTimeout);
    }
    
    async loadFonts() {
        try {
            if (!document.fonts) {
                console.warn('Font Loading API not supported, using fallbacks');
                this.useFallback();
                return;
            }
            
            // Wait for fonts to be ready
            await document.fonts.ready;
            
            // Check if primary fonts are loaded
            const primaryLoaded = this.checkPrimaryFonts();
            
            if (primaryLoaded) {
                this.usePrimaryFont();
            } else {
                // Try to load fonts manually
                await this.loadFontsManually();
            }
            
        } catch (error) {
            console.error('Font loading error:', error);
            this.useFallback();
        }
    }
    
    checkPrimaryFonts() {
        for (const font of this.fonts.primary) {
            if (this.isFontLoaded(font)) {
                console.log(`Primary font loaded: ${font}`);
                return true;
            }
        }
        return false;
    }
    
    checkFallbackFonts() {
        for (const font of this.fonts.fallback) {
            if (this.isFontLoaded(font)) {
                console.log(`Fallback font loaded: ${font}`);
                return true;
            }
        }
        return false;
    }
    
    isFontLoaded(fontName) {
        try {
            return document.fonts.check(`16px "${fontName}"`);
        } catch (error) {
            console.warn(`Error checking font ${fontName}:`, error);
            return false;
        }
    }
    
    async loadFontsManually() {
        const fontPromises = [];
        
        // Try to load primary fonts
        for (const fontName of this.fonts.primary) {
            const fontFace = new FontFace(fontName, `url(https://fonts.bunny.net/css2?family=${fontName.replace(' ', '+')}:wght@400;500;600;700&display=swap)`);
            fontPromises.push(
                fontFace.load().then(() => {
                    document.fonts.add(fontFace);
                    return fontName;
                }).catch(error => {
                    console.warn(`Failed to load ${fontName}:`, error);
                    return null;
                })
            );
        }
        
        try {
            const results = await Promise.allSettled(fontPromises);
            const loadedFonts = results
                .filter(result => result.status === 'fulfilled' && result.value)
                .map(result => result.value);
            
            if (loadedFonts.length > 0) {
                console.log('Manually loaded fonts:', loadedFonts);
                this.usePrimaryFont();
            } else {
                this.retryOrFallback();
            }
        } catch (error) {
            console.error('Manual font loading failed:', error);
            this.retryOrFallback();
        }
    }
    
    retryOrFallback() {
        if (this.currentRetry < this.retryCount) {
            this.currentRetry++;
            console.log(`Retrying font loading (${this.currentRetry}/${this.retryCount})`);
            setTimeout(() => this.loadFonts(), 1000);
        } else {
            console.log('Max retries reached, using fallback fonts');
            this.useFallback();
        }
    }
    
    usePrimaryFont() {
        document.body.classList.remove('font-loading', 'kalpurush-fallback');
        document.body.classList.add('font-loaded');
        
        // Trigger font transition animation
        this.triggerFontTransition();
        
        console.log('Primary Bengali fonts loaded successfully');
        
        // Dispatch custom event
        document.dispatchEvent(new CustomEvent('fontsLoaded', {
            detail: { type: 'primary', fonts: this.fonts.primary }
        }));
    }
    
    useFallback() {
        document.body.classList.remove('font-loading');
        document.body.classList.add('kalpurush-fallback');
        
        // Check if fallback fonts are available
        const fallbackAvailable = this.checkFallbackFonts();
        
        if (fallbackAvailable) {
            console.log('Using fallback Bengali fonts');
        } else {
            console.warn('Using system fallback fonts');
        }
        
        // Dispatch custom event
        document.dispatchEvent(new CustomEvent('fontsLoaded', {
            detail: { type: 'fallback', fonts: this.fonts.fallback }
        }));
    }
    
    triggerFontTransition() {
        // Add transition class to body for smooth font change
        document.body.classList.add('font-transition');
        
        // Remove transition class after animation
        setTimeout(() => {
            document.body.classList.remove('font-transition');
        }, 300);
    }
    
    // Public method to check current font status
    getCurrentFontStatus() {
        if (document.body.classList.contains('font-loaded')) {
            return 'primary';
        } else if (document.body.classList.contains('kalpurush-fallback')) {
            return 'fallback';
        } else {
            return 'loading';
        }
    }
    
    // Public method to force reload fonts
    reloadFonts() {
        this.currentRetry = 0;
        document.body.classList.remove('font-loaded', 'kalpurush-fallback');
        document.body.classList.add('font-loading');
        this.loadFonts();
    }
}

// Initialize font loader when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        window.fontLoader = new FontLoader();
    });
} else {
    window.fontLoader = new FontLoader();
}

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = FontLoader;
}
